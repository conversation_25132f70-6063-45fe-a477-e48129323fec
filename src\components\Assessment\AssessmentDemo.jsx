import { useState } from 'react';
import { motion } from 'framer-motion';
import ViaAssessment from './ViaAssessment';
import RiasecAssessment from './RiasecAssessment';
import BigFiveAssessment from './BigFiveAssessment';
import { transformAssessmentScores } from '../../utils/assessmentTransformers';

const AssessmentDemo = () => {
  const [assessmentData, setAssessmentData] = useState({
    via: {},
    riasec: {},
    bigFive: {}
  });
  const [completionStatus, setCompletionStatus] = useState({
    via: false,
    riasec: false,
    bigFive: false
  });
  const [activeTab, setActiveTab] = useState('via');

  const handleAssessmentUpdate = (type, data, isComplete) => {
    setAssessmentData(prev => ({
      ...prev,
      [type]: data
    }));
    
    setCompletionStatus(prev => ({
      ...prev,
      [type]: isComplete
    }));
  };

  const isAllComplete = completionStatus.via && completionStatus.riasec && completionStatus.bigFive;

  const handleTestTransform = () => {
    if (isAllComplete) {
      try {
        const transformedData = transformAssessmentScores(assessmentData);
        console.log('Transformed Assessment Data:', transformedData);
        alert('Check console for transformed data!');
      } catch (error) {
        console.error('Transform error:', error);
        alert('Transform error: ' + error.message);
      }
    } else {
      alert('Please complete all assessments first');
    }
  };

  const tabs = [
    { id: 'via', name: 'VIA Character Strengths', component: ViaAssessment },
    { id: 'riasec', name: 'RIASEC Holland Codes', component: RiasecAssessment },
    { id: 'bigFive', name: 'Big Five Inventory', component: BigFiveAssessment }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8 text-center"
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Assessment Demo
          </h1>
          <p className="text-gray-600">
            Test the assessment components and data transformation
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-white text-blue-600 shadow-sm'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {tab.name}
                {completionStatus[tab.id] && (
                  <span className="ml-2 text-green-600">✓</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Assessment Content */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {tabs.map((tab) => {
            const Component = tab.component;
            return (
              <div
                key={tab.id}
                className={activeTab === tab.id ? 'block' : 'hidden'}
              >
                <Component
                  data={assessmentData[tab.id]}
                  onUpdate={(data, isComplete) => handleAssessmentUpdate(tab.id, data, isComplete)}
                  isActive={activeTab === tab.id}
                />
              </div>
            );
          })}
        </div>

        {/* Debug Info */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Completion Status */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Completion Status</h3>
            <div className="space-y-2">
              {Object.entries(completionStatus).map(([key, isComplete]) => (
                <div key={key} className="flex items-center justify-between">
                  <span className="text-gray-600 capitalize">{key}:</span>
                  <span className={`font-medium ${isComplete ? 'text-green-600' : 'text-red-600'}`}>
                    {isComplete ? 'Complete' : 'Incomplete'}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Score Summary */}
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Score Summary</h3>
            <div className="space-y-2 text-sm">
              {Object.entries(assessmentData).map(([type, data]) => (
                <div key={type}>
                  <span className="font-medium text-gray-700 capitalize">{type}:</span>
                  <span className="ml-2 text-gray-600">
                    {Object.keys(data).length} categories scored
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Test Transform Button */}
        <div className="mt-8 text-center">
          <button
            onClick={handleTestTransform}
            disabled={!isAllComplete}
            className={`px-6 py-3 rounded-lg font-medium transition-all ${
              isAllComplete
                ? 'bg-blue-600 hover:bg-blue-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Test Data Transformation
          </button>
          {!isAllComplete && (
            <p className="text-sm text-gray-600 mt-2">
              Complete all assessments to test transformation
            </p>
          )}
        </div>

        {/* Raw Data Display */}
        <div className="mt-8 bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Raw Assessment Data</h3>
          <pre className="text-xs bg-gray-50 p-4 rounded overflow-auto max-h-96">
            {JSON.stringify(assessmentData, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
};

export default AssessmentDemo;
