import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, AlertCircle, ArrowLeft, Eye } from 'lucide-react';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import apiService from '../../services/apiService';

const AssessmentStatus = () => {
  const { jobId } = useParams();
  const navigate = useNavigate();
  const [status, setStatus] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pollingInterval, setPollingInterval] = useState(null);

  useEffect(() => {
    if (jobId) {
      checkStatus();
      // Start polling for status updates
      const interval = setInterval(checkStatus, 3000); // Poll every 3 seconds
      setPollingInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [jobId]);

  const checkStatus = async () => {
    try {
      const response = await apiService.getAssessmentStatus(jobId);
      setStatus(response);
      setError(null);
      
      // Stop polling if job is complete or failed
      if (response.status === 'completed' || response.status === 'failed') {
        if (pollingInterval) {
          clearInterval(pollingInterval);
          setPollingInterval(null);
        }
      }
    } catch (error) {
      console.error('Error checking assessment status:', error);
      setError(error.response?.data?.message || 'Failed to check assessment status');
    } finally {
      setLoading(false);
    }
  };

  const getStatusConfig = () => {
    if (!status) return null;

    switch (status.status) {
      case 'pending':
        return {
          icon: Clock,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Assessment Queued',
          description: 'Your assessment is in the queue and will be processed shortly.'
        };
      case 'processing':
        return {
          icon: LoadingSpinner,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Processing Assessment',
          description: 'AI is analyzing your responses and generating insights.'
        };
      case 'completed':
        return {
          icon: CheckCircle,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Assessment Complete',
          description: 'Your assessment has been successfully analyzed!'
        };
      case 'failed':
        return {
          icon: AlertCircle,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Assessment Failed',
          description: 'There was an error processing your assessment.'
        };
      default:
        return {
          icon: Clock,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          description: 'Assessment status is unknown.'
        };
    }
  };

  const handleViewResults = () => {
    if (status?.result_id) {
      navigate(`/results/${status.result_id}`);
    }
  };

  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };

  const handleRetryAssessment = () => {
    navigate('/assessment');
  };

  if (loading && !status) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <p className="text-slate-600">Checking assessment status...</p>
        </div>
      </div>
    );
  }

  if (error && !status) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="max-w-md mx-auto p-6">
          <ErrorMessage message={error} />
          <div className="mt-4 text-center">
            <button
              onClick={handleBackToDashboard}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  const statusConfig = getStatusConfig();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <button
            onClick={handleBackToDashboard}
            className="flex items-center text-slate-600 hover:text-slate-900 mb-4 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </button>
          
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-2">
              Assessment Status
            </h1>
            <p className="text-slate-600">
              Job ID: {jobId}
            </p>
          </div>
        </motion.div>

        {/* Status Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="max-w-2xl mx-auto"
        >
          <div className={`bg-white rounded-xl shadow-sm border-2 p-8 ${statusConfig?.borderColor || 'border-slate-200'}`}>
            <div className="text-center">
              {/* Status Icon */}
              <div className={`inline-flex items-center justify-center w-16 h-16 rounded-full mb-6 ${statusConfig?.bgColor || 'bg-slate-50'}`}>
                {statusConfig?.icon && (
                  <statusConfig.icon className={`w-8 h-8 ${statusConfig.color}`} />
                )}
              </div>

              {/* Status Title */}
              <h2 className="text-2xl font-bold text-slate-900 mb-2">
                {statusConfig?.title || 'Processing...'}
              </h2>

              {/* Status Description */}
              <p className="text-slate-600 mb-6">
                {statusConfig?.description || 'Please wait while we process your assessment.'}
              </p>

              {/* Progress Information */}
              {status && (
                <div className="bg-slate-50 rounded-lg p-4 mb-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-slate-500">Status:</span>
                      <span className="ml-2 font-medium text-slate-900 capitalize">
                        {status.status}
                      </span>
                    </div>
                    <div>
                      <span className="text-slate-500">Submitted:</span>
                      <span className="ml-2 font-medium text-slate-900">
                        {new Date(status.created_at).toLocaleString()}
                      </span>
                    </div>
                    {status.updated_at && (
                      <div>
                        <span className="text-slate-500">Last Updated:</span>
                        <span className="ml-2 font-medium text-slate-900">
                          {new Date(status.updated_at).toLocaleString()}
                        </span>
                      </div>
                    )}
                    {status.estimated_completion && (
                      <div>
                        <span className="text-slate-500">Estimated Completion:</span>
                        <span className="ml-2 font-medium text-slate-900">
                          {new Date(status.estimated_completion).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                {status?.status === 'completed' && status?.result_id && (
                  <button
                    onClick={handleViewResults}
                    className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                  >
                    <Eye className="w-5 h-5 mr-2" />
                    View Results
                  </button>
                )}

                {status?.status === 'failed' && (
                  <button
                    onClick={handleRetryAssessment}
                    className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
                  >
                    Retry Assessment
                  </button>
                )}

                <button
                  onClick={handleBackToDashboard}
                  className="inline-flex items-center px-6 py-3 bg-slate-200 hover:bg-slate-300 text-slate-700 font-medium rounded-lg transition-colors"
                >
                  Back to Dashboard
                </button>
              </div>

              {/* Auto-refresh indicator */}
              {(status?.status === 'pending' || status?.status === 'processing') && (
                <p className="text-xs text-slate-500 mt-4">
                  This page will automatically update every 3 seconds
                </p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Additional Information */}
        {status?.status === 'processing' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="max-w-2xl mx-auto mt-8"
          >
            <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">What's happening now?</h3>
              <div className="space-y-3 text-sm text-slate-600">
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <div>
                    <strong>Analyzing VIA Character Strengths:</strong> Identifying your top character strengths and virtues
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <div>
                    <strong>Processing RIASEC Interests:</strong> Mapping your career interests and work preferences
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <div>
                    <strong>Evaluating Big Five Traits:</strong> Analyzing your personality dimensions
                  </div>
                </div>
                <div className="flex items-start">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <div>
                    <strong>Generating Career Persona:</strong> Creating personalized career recommendations
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default AssessmentStatus;
