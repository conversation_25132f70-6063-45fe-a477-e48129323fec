import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { CheckCircle, Clock, ArrowLeft, Send } from 'lucide-react';
import ViaAssessment from './ViaAssessment';
import RiasecAssessment from './RiasecAssessment';
import BigFiveAssessment from './BigFiveAssessment';
import LoadingSpinner from '../UI/LoadingSpinner';
import ErrorMessage from '../UI/ErrorMessage';
import apiService from '../../services/apiService';
import { transformAssessmentScores } from '../../utils/assessmentTransformers';

const AssessmentFlow = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('via');
  const [assessmentData, setAssessmentData] = useState({
    via: {},
    riasec: {},
    bigFive: {}
  });
  const [completionStatus, setCompletionStatus] = useState({
    via: false,
    riasec: false,
    bigFive: false
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState(null);

  // Load saved progress from localStorage
  useEffect(() => {
    const savedData = localStorage.getItem('assessmentProgress');
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData);
        setAssessmentData(parsed.data || { via: {}, riasec: {}, bigFive: {} });
        setCompletionStatus(parsed.status || { via: false, riasec: false, bigFive: false });
      } catch (error) {
        console.error('Error loading saved assessment data:', error);
      }
    }
  }, []);

  // Save progress to localStorage
  useEffect(() => {
    const progressData = {
      data: assessmentData,
      status: completionStatus,
      timestamp: Date.now()
    };
    localStorage.setItem('assessmentProgress', JSON.stringify(progressData));
  }, [assessmentData, completionStatus]);

  const handleAssessmentUpdate = (type, data, isComplete) => {
    setAssessmentData(prev => ({
      ...prev,
      [type]: data
    }));
    
    setCompletionStatus(prev => ({
      ...prev,
      [type]: isComplete
    }));
  };

  const isAllComplete = completionStatus.via && completionStatus.riasec && completionStatus.bigFive;

  const handleSubmit = async () => {
    if (!isAllComplete) {
      setSubmitError('Please complete all assessments before submitting.');
      return;
    }

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Transform scores to API format
      const transformedData = transformAssessmentScores(assessmentData);
      
      // Submit to API
      const response = await apiService.submitAssessment(transformedData);
      
      // Clear saved progress
      localStorage.removeItem('assessmentProgress');
      
      // Navigate to status page
      navigate(`/assessment/status/${response.job_id}`);
    } catch (error) {
      console.error('Assessment submission error:', error);
      setSubmitError(error.response?.data?.message || 'Failed to submit assessment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const tabs = [
    {
      id: 'via',
      name: 'Character Strengths',
      description: 'VIA-IS Assessment',
      component: ViaAssessment
    },
    {
      id: 'riasec',
      name: 'Career Interests',
      description: 'RIASEC Holland Codes',
      component: RiasecAssessment
    },
    {
      id: 'bigFive',
      name: 'Personality Traits',
      description: 'Big Five Inventory',
      component: BigFiveAssessment
    }
  ];

  const getTabIcon = (tabId) => {
    if (completionStatus[tabId]) {
      return <CheckCircle className="w-5 h-5 text-emerald-600" />;
    }
    return <Clock className="w-5 h-5 text-slate-400" />;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <button
            onClick={() => navigate('/dashboard')}
            className="flex items-center text-slate-600 hover:text-slate-900 mb-4 transition-colors"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Dashboard
          </button>
          
          <div className="text-center">
            <h1 className="text-3xl md:text-4xl font-bold text-slate-900 mb-2">
              AI-Driven Talent Mapping Assessment
            </h1>
            <p className="text-slate-600 max-w-2xl mx-auto">
              Complete all three assessments to get comprehensive insights into your personality, 
              career interests, and character strengths.
            </p>
          </div>
        </motion.div>

        {/* Progress Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 mb-8"
        >
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-slate-900">Assessment Progress</h2>
            <div className="text-sm text-slate-600">
              {Object.values(completionStatus).filter(Boolean).length} of 3 completed
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {tabs.map((tab) => (
              <div
                key={tab.id}
                className={`p-4 rounded-lg border-2 transition-all cursor-pointer ${
                  activeTab === tab.id
                    ? 'border-blue-500 bg-blue-50'
                    : completionStatus[tab.id]
                    ? 'border-emerald-200 bg-emerald-50'
                    : 'border-slate-200 bg-slate-50'
                }`}
                onClick={() => setActiveTab(tab.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-slate-900">{tab.name}</h3>
                  {getTabIcon(tab.id)}
                </div>
                <p className="text-sm text-slate-600">{tab.description}</p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Assessment Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden"
        >
          {tabs.map((tab) => {
            const Component = tab.component;
            return (
              <div
                key={tab.id}
                className={activeTab === tab.id ? 'block' : 'hidden'}
              >
                <Component
                  data={assessmentData[tab.id]}
                  onUpdate={(data, isComplete) => handleAssessmentUpdate(tab.id, data, isComplete)}
                  isActive={activeTab === tab.id}
                />
              </div>
            );
          })}
        </motion.div>

        {/* Submit Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="mt-8 text-center"
        >
          {submitError && (
            <div className="mb-4">
              <ErrorMessage message={submitError} />
            </div>
          )}
          
          <button
            onClick={handleSubmit}
            disabled={!isAllComplete || isSubmitting}
            className={`inline-flex items-center px-8 py-3 rounded-lg font-medium transition-all ${
              isAllComplete && !isSubmitting
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                : 'bg-slate-300 text-slate-500 cursor-not-allowed'
            }`}
          >
            {isSubmitting ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Submitting Assessment...
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                Submit Assessment
              </>
            )}
          </button>
          
          {!isAllComplete && (
            <p className="text-sm text-slate-600 mt-2">
              Complete all three assessments to submit
            </p>
          )}
        </motion.div>
      </div>
    </div>
  );
};

export default AssessmentFlow;
